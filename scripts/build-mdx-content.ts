#!/usr/bin/env tsx

/**
 * MDX Content Build Script
 * 
 * This script compiles MDX files into a JSON bundle for use with
 * the NextMDXRemoteProvider. It supports both one-time builds
 * and watch mode for development.
 */

import matter from 'gray-matter'
import glob from 'fast-glob'
import fs from 'fs-extra'
import path from 'path'
import { watch } from 'chokidar'
import type { ContentBundle } from '../src/services/content/providers/next-mdx-remote/types'

/**
 * Build options
 */
interface BuildOptions {
  /** Enable watch mode for development */
  watch?: boolean
  /** Content directory path */
  contentDir?: string
  /** Output directory path */
  outputDir?: string
}

/**
 * Main build function
 * 
 * Compiles all MDX files into a JSON bundle
 */
async function buildMDXContent(options: BuildOptions = {}) {
  const {
    watch: watchMode = false,
    contentDir = './content',
    outputDir = './.mdx-compiled'
  } = options
  
  console.log('[MDX Build] Starting build process...')
  const startTime = Date.now()
  
  try {
    // Find all MDX files
    const pattern = path.join(contentDir, '**/*.mdx')
    const files = await glob(pattern, {
      ignore: ['**/node_modules/**', '**/_*/**', '**/_*']
    })
    
    if (files.length === 0) {
      console.warn('[MDX Build] No MDX files found in', contentDir)
      return
    }
    
    console.log(`[MDX Build] Found ${files.length} MDX files`)
    
    // Compile all files in parallel
    const contentMap: ContentBundle = {}
    const compilationResults = await Promise.all(
      files.map(file => compileFile(file, contentDir))
    )
    
    // Build content map
    compilationResults.forEach(result => {
      if (result) {
        contentMap[result.key] = result.content
      }
    })
    
    // Ensure output directory exists
    await fs.ensureDir(outputDir)
    
    // Write full bundle
    const fullBundlePath = path.join(outputDir, 'content-bundle.json')
    await fs.writeJSON(fullBundlePath, contentMap, { spaces: 2 })
    console.log(`[MDX Build] Written full bundle to ${fullBundlePath}`)
    
    // Write minified bundle for Workers
    const minifiedMap = minifyContentBundle(contentMap)
    const minBundlePath = path.join(outputDir, 'content-bundle.min.json')
    await fs.writeJSON(minBundlePath, minifiedMap)
    console.log(`[MDX Build] Written minified bundle to ${minBundlePath}`)
    
    const duration = Date.now() - startTime
    console.log(`[MDX Build] ✓ Build completed in ${duration}ms`)
    
    // Start watch mode if requested
    if (watchMode) {
      startWatcher(contentDir, options)
    }
  } catch (error) {
    console.error('[MDX Build] Build failed:', error)
    process.exit(1)
  }
}

/**
 * Compile a single MDX file
 * 
 * @param filePath - Path to the MDX file
 * @param contentDir - Base content directory
 * @returns Compiled content with key
 */
async function compileFile(
  filePath: string,
  contentDir: string
): Promise<{ key: string; content: any } | null> {
  try {
    // Read file content
    const source = await fs.readFile(filePath, 'utf8')
    const { content, data } = matter(source)
    
    // Extract metadata from file path
    const relativePath = path.relative(contentDir, filePath)
    const pathParts = relativePath.split(path.sep)
    
    // Expected structure: content/[type]/[locale]/[slug].mdx
    if (pathParts.length < 3) {
      console.warn(`[MDX Build] Invalid file structure: ${filePath}`)
      return null
    }
    
    const type = normalizeContentType(pathParts[0])
    const locale = pathParts[1]
    const slug = path.basename(pathParts[pathParts.length - 1], '.mdx')
    
    // For now, we'll store the raw MDX content and let next-mdx-remote handle compilation at runtime
    // This avoids build-time dependency issues
    
    // Build content object
    const compiledContent = {
      slug,
      type,
      lang: locale,
      url: buildContentUrl(type, slug, locale),
      title: data.title || slug,
      description: data.description || '',
      body: {
        html: '', // Will be compiled at runtime
        raw: content
      },
      createdAt: data.date || data.createdAt || new Date().toISOString(),
      publishedAt: data.publishedAt,
      featured: data.featured || false,
      tags: data.tags || [],
      author: data.author,
      coverImage: data.coverImage || data.image,
      ...data // Include any additional frontmatter
    }
    
    const key = `${type}-${slug}-${locale}`
    console.log(`[MDX Build] Compiled: ${key}`)
    
    return { key, content: compiledContent }
  } catch (error) {
    console.error(`[MDX Build] Failed to compile ${filePath}:`, error)
    return null
  }
}

/**
 * Normalize content type from directory name
 * 
 * @param dirName - Directory name (e.g., 'blogs', 'case-studies')
 * @returns Normalized type (e.g., 'blog', 'case-study')
 */
function normalizeContentType(dirName: string): string {
  if (dirName === 'case-studies') {
    return 'case-study'
  }
  // Remove trailing 's' for other types
  return dirName.endsWith('s') ? dirName.slice(0, -1) : dirName
}

/**
 * Build content URL
 * 
 * @param type - Content type
 * @param slug - Content slug
 * @param locale - Language locale
 * @returns Full URL path
 */
function buildContentUrl(type: string, slug: string, locale: string): string {
  const typeSegment = type === 'case-study' ? 'case-studies' : `${type}s`
  const localePrefix = locale === 'en' ? '' : `/${locale}`
  return `${localePrefix}/${typeSegment}/${slug}`
}

/**
 * Minify content bundle for production/Workers
 * 
 * @param bundle - Full content bundle
 * @returns Minified bundle
 */
function minifyContentBundle(bundle: ContentBundle): ContentBundle {
  const minified: ContentBundle = {}
  
  Object.entries(bundle).forEach(([key, content]) => {
    minified[key] = {
      ...content,
      body: {
        html: content.body.html
        // Remove raw content to save space
      }
    } as any
  })
  
  return minified
}

/**
 * Start file watcher for development
 * 
 * @param contentDir - Content directory to watch
 * @param options - Build options
 */
function startWatcher(contentDir: string, options: BuildOptions) {
  console.log('[MDX Build] Starting watch mode...')
  
  const watcher = watch(path.join(contentDir, '**/*.mdx'), {
    ignoreInitial: true,
    ignored: ['**/node_modules/**', '**/_*/**', '**/_*']
  })
  
  let rebuildTimeout: NodeJS.Timeout | null = null
  
  const scheduleRebuild = () => {
    // Debounce rebuilds
    if (rebuildTimeout) {
      clearTimeout(rebuildTimeout)
    }
    
    rebuildTimeout = setTimeout(async () => {
      console.log('[MDX Build] Changes detected, rebuilding...')
      await buildMDXContent({ ...options, watch: false })
    }, 300)
  }
  
  watcher
    .on('add', path => {
      console.log(`[MDX Build] File added: ${path}`)
      scheduleRebuild()
    })
    .on('change', path => {
      console.log(`[MDX Build] File changed: ${path}`)
      scheduleRebuild()
    })
    .on('unlink', path => {
      console.log(`[MDX Build] File removed: ${path}`)
      scheduleRebuild()
    })
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n[MDX Build] Shutting down watcher...')
    watcher.close()
    process.exit(0)
  })
}

// Export for programmatic use
export default buildMDXContent

// CLI entry point
if (require.main === module) {
  const args = process.argv.slice(2)
  const watchMode = args.includes('--watch') || args.includes('-w')
  
  buildMDXContent({ watch: watchMode }).catch(error => {
    console.error('[MDX Build] Fatal error:', error)
    process.exit(1)
  })
}