/**
 * Content Type Definitions
 * 
 * This module provides comprehensive type definitions for content items
 * in the content management system. These types are designed to be provider-agnostic,
 * allowing for seamless switching between different CMS backends while
 * maintaining type safety and consistent API interfaces.
 * 
 * The content types support various media formats, multilingual content,
 * and rich metadata for SEO and content management purposes.
 */

/**
 * Supported content types in the system
 */
export type ContentType = 'blog' | 'product' | 'case-study'

/**
 * MDX Content Interface
 *
 * Represents processed MDX content with both raw source and compiled code.
 * This structure supports both server-side rendering and client-side hydration
 * by providing access to both the original markdown and the compiled JavaScript.
 */
export interface MDXContent {
  /** Raw markdown/MDX source content */
  raw: string
  /** Compiled JavaScript code for rendering */
  code: string
}

/**
 * Base Content Item Interface
 * 
 * Defines the common structure that all content items must have
 * regardless of their specific type. This ensures consistency
 * across different content types and CMS providers.
 */
export interface BaseContentItem {
  // Core identification fields
  /** Unique slug identifier for the content */
  slug: string
  /** Display title of the content */
  title: string
  /** Language code (ISO 639-1) */
  lang: string
  /** Full URL path to the content */
  url: string
  
  // Content and metadata
  /** Optional short description or excerpt */
  description?: string
  /** Main content body in MDX format */
  body: MDXContent
  
  // Media and visual content
  /** URL to the cover/featured image */
  coverImage?: string
  /** URL to the author's profile image */
  authorImage?: string
  /** URL to an associated video */
  videoUrl?: string
  /** URL to the video thumbnail image */
  videoThumbnail?: string
  /** Duration of the video in human-readable format */
  videoDuration?: string
  
  // Publishing information
  /** Author name or identifier */
  author?: string
  /** Publication date in ISO 8601 format */
  publishedAt?: string
  /** Creation date in ISO 8601 format */
  createdAt: string
  /** Whether this content is featured/highlighted */
  featured: boolean
  /** Array of tags for categorization */
  tags?: string[]
}

/**
 * Blog Content Interface
 *
 * Represents blog post content with blog-specific properties.
 * Blog content typically focuses on articles, news, and
 * editorial content with strong emphasis on publishing
 * dates and author information.
 */
export interface BlogContent extends BaseContentItem {
  /** Content type identifier */
  type: 'blog'
}

/**
 * Product Content Interface
 *
 * Represents product or service content with product-specific
 * properties. Product content includes an optional icon field
 * for visual representation in product listings and catalogs.
 */
export interface ProductContent extends BaseContentItem {
  /** Content type identifier */
  type: 'product'
  /** Optional icon URL for product representation */
  icon?: string
}

/**
 * Case Study Content Interface
 *
 * Represents case study content showcasing projects,
 * implementations, or success stories. Case studies
 * typically include detailed project information and
 * outcomes.
 */
export interface CaseStudyContent extends BaseContentItem {
  /** Content type identifier */
  type: 'case-study'
}

/**
 * Content Item Union Type
 *
 * Represents any type of content item in the system.
 * This union type allows for type-safe handling of different
 * content types while maintaining their specific properties.
 */
export type ContentItem = BlogContent | ProductContent | CaseStudyContent

/**
 * Content Metadata Interface
 * 
 * Represents metadata information about content items
 * including computed properties like reading time and
 * word count that are useful for SEO and UX features.
 */
export interface ContentMetadata {
  /** Total word count in the content body */
  wordCount: number
  /** Estimated reading time in minutes */
  readingTime: number
  /** Publication date in ISO 8601 format */
  publishedAt?: string
  /** Last update date in ISO 8601 format */
  updatedAt?: string
  /** Author name or identifier */
  author?: string
  /** Array of content tags */
  tags: string[]
}

/**
 * Language Version Interface
 * 
 * Represents different language versions of the same content.
 * This is used for implementing language switching functionality
 * and providing users with alternative language options.
 */
export interface LanguageVersion {
  /** Language code (ISO 639-1) */
  lang: string
  /** Localized title of the content */
  title: string
  /** URL to the localized version */
  url: string
  /** Whether this language version is available */
  available: boolean
}

/**
 * Content Type Mapping
 * 
 * Maps content type identifiers to their corresponding
 * TypeScript interfaces for type-safe content handling.
 */
export type ContentTypeMap = {
  blog: BlogContent
  product: ProductContent
  'case-study': CaseStudyContent
}

/**
 * Content By Type
 * 
 * Utility type that resolves a content type identifier
 * to its corresponding content interface.
 */
export type ContentByType<T extends ContentType> = ContentTypeMap[T]