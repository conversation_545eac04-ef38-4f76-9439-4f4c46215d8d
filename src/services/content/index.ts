/**
 * Content Service Public API
 * 
 * This is the main entry point for the content management service.
 * It provides a unified interface for all content-related operations.
 */

// Import core components
import { ContentService, ProviderFactory, ConfigManager } from './core'
import { defaultConfig } from './types'

// Initialize default content service
const config = ConfigManager.createFromSources(
  defaultConfig,
  ConfigManager.loadFromEnvironment(),
  ConfigManager.getEnvironmentConfig()
)

ConfigManager.initialize(config)
const provider = ProviderFactory.create(config.provider, config.providerConfig)
const contentService = new ContentService(provider, config)

// Export the default instance
export { contentService }

// Export all types
export * from './types'

// Import modules that need wrapping
import {
  getAvailableLanguageVersions as _getAvailableLanguageVersions,
  handleContentLanguageSwitch as _handleContentLanguageSwitch,
  generateHreflangAttributes as _generateHreflangAttributes
} from './modules/language-switching'

import {
  generateStaticParams as _generateStaticParams,
  getAllContent as _getAllContent,
  getContentByLocale as _getContentByLocale,
  getContentStatistics as _getContentStatistics,
  validateContentForBuild as _validateContentForBuild
} from './modules/static-generation'

import {
  getRelatedContent as _getRelatedContent,
  getContentByTags as _getContentByTags,
  getFeaturedContent as _getFeaturedContent,
  getRecentContent as _getRecentContent,
  getContentByAuthor as _getContentByAuthor,
  getContentRecommendations as _getContentRecommendations
} from './modules/content-relations'

// Export wrapped versions of language switching functions
export const getAvailableLanguageVersions = (
  contentType: import('./types').ContentType,
  slug: string,
  currentLocale: string = 'en'
) => _getAvailableLanguageVersions(contentService, contentType, slug, currentLocale)

export const handleContentLanguageSwitch = (params: import('./modules').LanguageSwitchParams) =>
  _handleContentLanguageSwitch(contentService, params)

export const generateHreflangAttributes = (
  contentType: import('./types').ContentType,
  slug: string,
  baseUrl: string = ''
) => _generateHreflangAttributes(contentService, contentType, slug, baseUrl)

// Export wrapped versions of static generation functions
export const generateStaticParams = (type: import('./types').ContentType) =>
  _generateStaticParams(contentService, type)

export const getAllContent = <T extends import('./types').ContentItem>(type: import('./types').ContentType) =>
  _getAllContent<T>(contentService, type)

export const getContentByLocale = <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string
) => _getContentByLocale<T>(contentService, type, locale)

export const getContentStatistics = () => _getContentStatistics(contentService)

export const validateContentForBuild = () => _validateContentForBuild(contentService)

// Export wrapped versions of content relations functions
export const getRelatedContent = <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  currentSlug: string,
  locale: string,
  limit?: number
) => _getRelatedContent<T>(contentService, type, currentSlug, locale, limit)

export const getContentByTags = <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  tags: string[],
  locale: string,
  limit?: number
) => _getContentByTags<T>(contentService, type, tags, locale, limit)

export const getFeaturedContent = <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string,
  limit?: number
) => _getFeaturedContent<T>(contentService, type, locale, limit)

export const getRecentContent = <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string,
  limit?: number
) => _getRecentContent<T>(contentService, type, locale, limit)

export const getContentByAuthor = <T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  author: string,
  locale: string,
  limit?: number
) => _getContentByAuthor<T>(contentService, type, author, locale, limit)

export const getContentRecommendations = <T extends import('./types').ContentItem>(
  currentContent: import('./types').ContentItem,
  limit?: number
) => _getContentRecommendations<T>(contentService, currentContent, limit)

// Export types and functions that don't need wrapping
export {
  type LanguageSwitchParams,
  type LanguageSwitchResult,
  generateSEOMetadata,
  generateListPageSEOMetadata,
  type SEOMetadata
} from './modules'

// Export utilities
export * from './utils'

// Export core classes for custom instantiation
export { ContentService, ProviderFactory, ConfigManager } from './core'

// Export providers
export { Contentlayer2Provider } from './providers'

// Helper function to create a custom content service instance
export function createContentService(customConfig?: Partial<import('./types').ContentServiceConfig>) {
  const mergedConfig = ConfigManager.createFromSources(
    defaultConfig,
    ConfigManager.loadFromEnvironment(),
    ConfigManager.getEnvironmentConfig(),
    customConfig || {}
  )
  
  ConfigManager.initialize(mergedConfig)
  const customProvider = ProviderFactory.create(mergedConfig.provider, mergedConfig.providerConfig)
  return new ContentService(customProvider, mergedConfig)
}

// Helper function to get content title
export async function getContentTitle(
  type: import('./types').ContentType,
  slug: string,
  locale: string
): Promise<string | null> {
  return contentService.getContentTitle(type, slug, locale)
}

// Helper function to get single content
export async function getContent<T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  slug: string,
  locale: string
): Promise<T | null> {
  return contentService.getContent<T>(type, slug, locale)
}

// Helper function to get content list
export async function getContentList<T extends import('./types').ContentItem>(
  type: import('./types').ContentType,
  locale: string,
  options?: import('./types').QueryOptions
): Promise<T[]> {
  return contentService.getContentList<T>(type, locale, options)
}

// Helper function to get all content slugs for static generation
export async function getAllContentSlugs(
  type: import('./types').ContentType
): Promise<Array<{ slug: string; locale: string }>> {
  return contentService.getAllContentSlugs(type)
}
