/**
 * Language Switching Module
 * 
 * Provides intelligent language switching functionality for content pages
 * with sophisticated fallback strategies when content doesn't exist.
 */

import type { 
  ContentType,
  LanguageVersion
} from '../types'
import type { ContentService } from '../core/content-service'

/**
 * Language switch parameters
 */
export interface LanguageSwitchParams {
  currentPath: string
  currentLocale: string
  targetLocale: string
  baseUrl?: string
  context?: {
    contentType?: string
    slug?: string
  }
}

/**
 * Language switch result
 */
export interface LanguageSwitchResult {
  url: string
  strategy: 'direct' | 'fallback' | 'home'
  available: boolean
  metadata?: {
    fallbackReason?: string
    originalTitle?: string
    targetTitle?: string
  }
}

/**
 * Get available language versions for content
 * 
 * @param service - Content service instance
 * @param contentType - Type of content
 * @param slug - Content identifier
 * @param currentLocale - Current locale
 * @returns Array of language versions
 */
export async function getAvailableLanguageVersions(
  service: ContentService,
  contentType: ContentType,
  slug: string,
  currentLocale: string = 'en'
): Promise<LanguageVersion[]> {
  const supportedLocales = service.getSupportedLocales()
  
  if (!slug) {
    return supportedLocales.map(lang => ({
      lang,
      title: '',
      url: `/${lang}/${contentType}s`,
      available: false
    }))
  }

  return service.getAvailableLanguages(contentType, slug)
}

/**
 * Handle content language switch
 * 
 * @param service - Content service instance
 * @param params - Language switch parameters
 * @returns Language switch result
 */
export async function handleContentLanguageSwitch(
  service: ContentService,
  params: LanguageSwitchParams
): Promise<LanguageSwitchResult> {
  const { currentPath, currentLocale, targetLocale, baseUrl = '', context } = params

  // If switching to the same locale, return current path
  if (currentLocale === targetLocale) {
    return {
      url: currentPath,
      strategy: 'direct',
      available: true,
      metadata: {
        fallbackReason: 'Same locale requested'
      }
    }
  }

  // If context is provided (we know the content type and slug)
  if (context?.contentType && context?.slug) {
    const contentType = context.contentType as ContentType
    const contentExists = await service.contentExists(contentType, context.slug, targetLocale)

    if (contentExists) {
      // Direct navigation to content in target locale
      const targetTitle = await service.getContentTitle(contentType, context.slug, targetLocale)
      const url = generateContentUrl(contentType, context.slug, targetLocale, baseUrl)

      return {
        url,
        strategy: 'direct',
        available: true,
        metadata: {
          targetTitle: targetTitle || undefined
        }
      }
    } else {
      // Fallback to content list page
      const url = generateContentListUrl(contentType, targetLocale, baseUrl)
      
      return {
        url,
        strategy: 'fallback',
        available: false,
        metadata: {
          fallbackReason: `Content "${context.slug}" not available in target language`
        }
      }
    }
  }

  // For non-content pages, generate a simple locale switch
  return handleGenericLanguageSwitch(currentPath, currentLocale, targetLocale, baseUrl)
}

/**
 * Generate hreflang attributes for SEO
 * 
 * @param service - Content service instance
 * @param contentType - Content type
 * @param slug - Content slug
 * @param baseUrl - Base URL for absolute URLs
 * @returns Array of hreflang attributes
 */
export async function generateHreflangAttributes(
  service: ContentService,
  contentType: ContentType,
  slug: string,
  baseUrl: string = ''
): Promise<Array<{ hreflang: string; href: string }>> {
  const versions = await service.getAvailableLanguages(contentType, slug)
  
  return versions
    .filter(version => version.available)
    .map(version => ({
      hreflang: version.lang,
      href: `${baseUrl}${version.url}`
    }))
}

// Helper functions

function generateContentUrl(
  contentType: ContentType,
  slug: string,
  locale: string,
  baseUrl: string
): string {
  const prefix = locale === 'en' ? '' : `/${locale}`
  const typeSegment = contentType === 'case-study' ? 'case-studies' : `${contentType}s`
  return `${baseUrl}${prefix}/${typeSegment}/${slug}`
}

function generateContentListUrl(
  contentType: ContentType,
  locale: string,
  baseUrl: string
): string {
  const prefix = locale === 'en' ? '' : `/${locale}`
  const typeSegment = contentType === 'case-study' ? 'case-studies' : `${contentType}s`
  return `${baseUrl}${prefix}/${typeSegment}`
}

function handleGenericLanguageSwitch(
  currentPath: string,
  currentLocale: string,
  targetLocale: string,
  baseUrl: string
): LanguageSwitchResult {
  // Remove current locale prefix if present
  let cleanPath = currentPath
  if (currentLocale !== 'en' && currentPath.startsWith(`/${currentLocale}`)) {
    cleanPath = currentPath.replace(`/${currentLocale}`, '') || '/'
  }

  // Add target locale prefix if not default locale
  const targetPath = targetLocale === 'en' 
    ? cleanPath 
    : `/${targetLocale}${cleanPath}`

  return {
    url: `${baseUrl}${targetPath}`,
    strategy: 'direct',
    available: true,
    metadata: {
      fallbackReason: 'Generic path language switch'
    }
  }
}

