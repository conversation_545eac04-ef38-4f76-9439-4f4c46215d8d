/**
 * Provider Factory
 * 
 * Factory class responsible for creating content provider instances
 * based on configuration. This allows for easy switching between
 * different CMS backends without changing application code.
 */

import type { ContentProvider, ProviderType, ProviderConfig } from '../types'
import { Contentlayer2Provider } from '../providers'
import { NextMDXRemoteProvider } from '../providers/next-mdx-remote'

/**
 * Provider Factory Class
 * 
 * Creates and configures content provider instances based on the
 * specified provider type and configuration.
 */
export class ProviderFactory {
  /**
   * Create a content provider instance
   * 
   * @param type - The provider type to create
   * @param config - Provider-specific configuration
   * @returns Configured content provider instance
   * @throws Error if provider type is not supported
   */
  static create(type: ProviderType, config?: ProviderConfig): ContentProvider {
    switch (type) {
      case 'contentlayer2':
        return new Contentlayer2Provider()
      
      case 'mdx':
        throw new Error('MDX provider is not yet implemented')
      
      case 'next-mdx-remote':
        return new NextMDXRemoteProvider(config)
      
      case 'strapi':
        throw new Error('Strapi provider is not yet implemented')
      
      case 'sanity':
        throw new Error('Sanity provider is not yet implemented')
      
      default:
        throw new Error(`Unknown provider type: ${type}`)
    }
  }

  /**
   * Check if a provider type is supported
   * 
   * @param type - The provider type to check
   * @returns True if the provider is supported
   */
  static isSupported(type: string): type is ProviderType {
    return ['contentlayer2', 'mdx', 'next-mdx-remote', 'strapi', 'sanity'].includes(type)
  }

  /**
   * Get list of available provider types
   * 
   * @returns Array of supported provider types
   */
  static getAvailableProviders(): ProviderType[] {
    return ['contentlayer2', 'mdx', 'next-mdx-remote', 'strapi', 'sanity']
  }

  /**
   * Get provider implementation status
   * 
   * @returns Object mapping provider types to their implementation status
   */
  static getProviderStatus(): Record<ProviderType, { implemented: boolean; description: string }> {
    return {
      contentlayer2: {
        implemented: true,
        description: 'File-based CMS with TypeScript support'
      },
      mdx: {
        implemented: false,
        description: 'Next.js built-in MDX support'
      },
      'next-mdx-remote': {
        implemented: true,
        description: 'MDX with pre-compilation for edge environments'
      },
      strapi: {
        implemented: false,
        description: 'Headless CMS with REST/GraphQL API'
      },
      sanity: {
        implemented: false,
        description: 'Real-time headless CMS'
      }
    }
  }
}