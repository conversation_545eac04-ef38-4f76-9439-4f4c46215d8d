/**
 * Configuration Management
 * 
 * Handles configuration loading, validation, and merging for the
 * content management service.
 */

import { ContentServiceConfig, defaultConfig } from '../types'

/**
 * Configuration manager for content service
 */
export class ConfigManager {
  private static instance: ConfigManager | null = null
  private config: ContentServiceConfig

  private constructor(config: ContentServiceConfig) {
    this.config = config
  }

  /**
   * Initialize the configuration manager
   * 
   * @param config - Partial configuration to merge with defaults
   * @returns Configuration manager instance
   */
  static initialize(config?: Partial<ContentServiceConfig>): ConfigManager {
    const mergedConfig = {
      ...defaultConfig,
      ...config,
      features: {
        ...defaultConfig.features,
        ...(config?.features || {})
      }
    }

    ConfigManager.instance = new ConfigManager(mergedConfig)
    return ConfigManager.instance
  }

  /**
   * Get the configuration manager instance
   * 
   * @returns Configuration manager instance
   * @throws Error if not initialized
   */
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      throw new Error('ConfigManager not initialized. Call initialize() first.')
    }
    return ConfigManager.instance
  }

  /**
   * Get the current configuration
   */
  getConfig(): ContentServiceConfig {
    return this.config
  }

  /**
   * Update configuration
   * 
   * @param updates - Partial configuration updates
   */
  updateConfig(updates: Partial<ContentServiceConfig>): void {
    this.config = {
      ...this.config,
      ...updates,
      features: {
        ...this.config.features,
        ...(updates.features || {})
      }
    }
  }

  /**
   * Get configuration for a specific environment
   * 
   * @param env - Environment name
   * @returns Environment-specific configuration
   */
  static getEnvironmentConfig(env: string = process.env.NODE_ENV || 'development'): Partial<ContentServiceConfig> {
    switch (env) {
      case 'development':
        return {
          debug: true,
          features: {
            seo: false,
            relatedContent: true,
            languageSwitching: true,
            staticGeneration: true
          }
        }
      
      case 'production':
        return {
          debug: false,
          features: {
            seo: true,
            relatedContent: true,
            languageSwitching: true,
            staticGeneration: true
          }
        }
      
      case 'test':
        return {
          debug: false,
          features: {
            seo: false,
            relatedContent: false,
            languageSwitching: false,
            staticGeneration: false
          }
        }
      
      default:
        return {}
    }
  }

  /**
   * Load configuration from environment variables
   * 
   * @returns Configuration from environment
   */
  static loadFromEnvironment(): Partial<ContentServiceConfig> {
    const config: Partial<ContentServiceConfig> = {}

    // Load base URL
    if (process.env.NEXT_PUBLIC_WEB_URL) {
      config.baseUrl = process.env.NEXT_PUBLIC_WEB_URL
    }

    // Load debug mode
    if (process.env.CONTENT_DEBUG) {
      config.debug = process.env.CONTENT_DEBUG === 'true'
    }

    // Load provider type
    if (process.env.CONTENT_PROVIDER) {
      config.provider = process.env.CONTENT_PROVIDER as any
    }

    return config
  }

  /**
   * Create configuration from multiple sources
   * 
   * @param sources - Configuration sources in order of precedence
   * @returns Merged configuration
   */
  static createFromSources(...sources: Partial<ContentServiceConfig>[]): ContentServiceConfig {
    let result = { ...defaultConfig }

    for (const source of sources) {
      result = {
        ...result,
        ...source,
        features: {
          ...result.features,
          ...(source.features || {})
        }
      }
    }

    return result
  }
}