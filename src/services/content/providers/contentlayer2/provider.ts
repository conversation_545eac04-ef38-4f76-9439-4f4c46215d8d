/**
 * Contentlayer2 Provider Implementation
 * 
 * This provider implements the content provider interface using Contentlayer2,
 * a community-maintained fork that supports Next.js 15 and React 19.
 * It provides type-safe content management with automatic TypeScript generation
 * and multi-language support.
 */

import type {
  ContentProvider,
  ContentItem,
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion,
  BlogContent,
  ProductContent,
  CaseStudyContent,
  MDXContent
} from '../../types'

// Contentlayer2 generated content imports
// These will be available after running 'contentlayer build'
import {
  allBlogs,
  allProducts,
  allCaseStudies,
  type Blog,
  type Product,
  type CaseStudy
} from 'contentlayer2/generated'

/**
 * Contentlayer2 Provider Class
 * 
 * Implements the ContentProvider interface using Contentlayer2 as the backend.
 */
export class Contentlayer2Provider implements ContentProvider {
  readonly name = 'contentlayer2'
  readonly version = '0.4.6'

  /**
   * Convert Contentlayer2 MDX to our abstract MDX type
   */
  private convertMDX(mdx: any): MDXContent {
    return {
      raw: mdx.raw || '',
      code: mdx.code || ''
    }
  }

  /**
   * Convert Contentlayer2 content to our abstract content types
   */
  private convertContent(item: Blog | Product | CaseStudy, type: ContentType): ContentItem {
    const baseContent = {
      slug: item.slug,
      title: item.title,
      lang: item.lang,
      url: item.url,
      description: item.description,
      body: this.convertMDX(item.body),
      coverImage: item.coverImage,
      authorImage: item.authorImage,
      videoUrl: item.videoUrl,
      videoThumbnail: item.videoThumbnail,
      videoDuration: item.videoDuration,
      author: item.author,
      publishedAt: item.publishedAt,
      createdAt: item.createdAt || new Date().toISOString(),
      featured: item.featured || false,
      tags: item.tags || []
    }

    switch (type) {
      case 'blog':
        return { ...baseContent, type: 'blog' } as BlogContent
      case 'product':
        return {
          ...baseContent,
          type: 'product',
          icon: (item as Product).icon
        } as ProductContent
      case 'case-study':
        return { ...baseContent, type: 'case-study' } as CaseStudyContent
      default:
        throw new Error(`Unknown content type: ${type}`)
    }
  }

  /**
   * Get content collection by type
   */
  private getCollection(type: ContentType): ContentItem[] {
    const rawCollections = {
      blog: allBlogs,
      product: allProducts,
      'case-study': allCaseStudies
    }

    const rawCollection = rawCollections[type] || []
    return rawCollection.map(item => this.convertContent(item, type))
  }

  /**
   * Get single content item
   */
  async getContent<T extends ContentItem>(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<T | null> {
    try {
      const collection = this.getCollection(type)
      const content = collection.find(item => 
        item.slug === slug && item.lang === locale
      )
      
      return (content as T) || null
    } catch (error) {
      console.error(`Error fetching content: ${type}/${locale}/${slug}`, error)
      return null
    }
  }

  /**
   * Get content list with filtering and sorting
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options: QueryOptions = {}
  ): Promise<T[]> {
    try {
      let content = this.getCollection(type)
        .filter(item => item.lang === locale) as T[]

      // Apply featured filter
      if (options.featured !== undefined) {
        content = content.filter(item => item.featured === options.featured)
      }

      // Apply tag filter
      if (options.tags && options.tags.length > 0) {
        content = content.filter(item => 
          item.tags && options.tags!.every(tag => item.tags!.includes(tag))
        )
      }

      // Apply author filter
      if (options.author) {
        content = content.filter(item => item.author === options.author)
      }

      // Apply sorting
      if (options.sortBy) {
        content.sort((a, b) => {
          const aValue = a[options.sortBy!]
          const bValue = b[options.sortBy!]
          const order = options.order === 'desc' ? -1 : 1
          
          if (typeof aValue === 'string' && typeof bValue === 'string') {
            return aValue.localeCompare(bValue) * order
          }
          
          if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
            return (aValue === bValue ? 0 : aValue ? -1 : 1) * order
          }
          
          return 0
        })
      }

      // Apply pagination
      const offset = options.offset || 0
      const limit = options.limit || content.length
      content = content.slice(offset, offset + limit)

      return content
    } catch (error) {
      console.error(`Error fetching content list: ${type}/${locale}`, error)
      return []
    }
  }

  /**
   * Check if content exists
   */
  async contentExists(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<boolean> {
    const content = await this.getContent(type, slug, locale)
    return content !== null
  }

  /**
   * Get content title
   */
  async getContentTitle(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<string | null> {
    const content = await this.getContent(type, slug, locale)
    return content?.title || null
  }

  /**
   * Get content metadata
   */
  async getContentMetadata(
    type: ContentType, 
    slug: string, 
    locale: string
  ): Promise<ContentMetadata | null> {
    const content = await this.getContent(type, slug, locale)
    
    if (!content) return null

    // Calculate reading time (approximate)
    const wordsPerMinute = 200
    const wordCount = content.body.raw?.split(/\s+/).length || 0
    const readingTime = Math.ceil(wordCount / wordsPerMinute)

    return {
      wordCount,
      readingTime,
      publishedAt: content.publishedAt,
      updatedAt: content.createdAt,
      author: content.author,
      tags: content.tags || []
    }
  }

  /**
   * Get available language versions
   */
  async getAvailableLanguages(
    type: ContentType, 
    slug: string
  ): Promise<LanguageVersion[]> {
    try {
      const collection = this.getCollection(type)
      const versions = collection
        .filter(item => item.slug === slug)
        .map(item => ({
          lang: item.lang,
          title: item.title,
          url: item.url,
          available: true
        }))

      return versions
    } catch (error) {
      console.error(`Error fetching language versions: ${type}/${slug}`, error)
      return []
    }
  }

  /**
   * Get related content
   */
  async getRelatedContent<T extends ContentItem>(
    type: ContentType,
    currentSlug: string,
    locale: string,
    limit: number = 3
  ): Promise<T[]> {
    try {
      const current = await this.getContent(type, currentSlug, locale)
      if (!current || !current.tags || current.tags.length === 0) return []

      const allContent = await this.getContentList<T>(type, locale)
      
      // Find content with matching tags
      const related = allContent
        .filter(item => item.slug !== currentSlug)
        .filter(item => {
          if (!item.tags || item.tags.length === 0) return false
          return current.tags!.some(tag => item.tags!.includes(tag))
        })
        .slice(0, limit)

      // If not enough related content, fill with recent content
      if (related.length < limit) {
        const recent = allContent
          .filter(item => item.slug !== currentSlug)
          .filter(item => !related.includes(item))
          .slice(0, limit - related.length)
        
        related.push(...recent)
      }

      return related
    } catch (error) {
      console.error(`Error fetching related content: ${type}/${locale}/${currentSlug}`, error)
      return []
    }
  }

  /**
   * Get content for static generation
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    try {
      const collection = this.getCollection(type)
      return collection as T[]
    } catch (error) {
      console.error(`Error fetching content for static generation: ${type}`, error)
      return []
    }
  }

  /**
   * Get all content slugs for static generation
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ locale: string; slug: string }>> {
    try {
      const collection = this.getCollection(type)
      return collection.map(item => ({
        locale: item.lang,
        slug: item.slug
      }))
    } catch (error) {
      console.error(`Error fetching content slugs: ${type}`, error)
      return []
    }
  }
}