# Next.js MDX Remote Migration Guide

This guide explains how to migrate from Contentlayer2 to the Next.js MDX Remote provider in the ShipAny project.

## Overview

The Next.js MDX Remote provider offers several advantages:
- **Cloudflare Workers Compatible**: Pre-compiled bundles work without file system access
- **Better Performance**: O(1) content lookups with pre-computed indexes
- **Simplified Build**: Single build script handles both development and production
- **Active Maintenance**: Built on actively maintained next-mdx-remote package

## Migration Steps

### 1. Update Environment Variables

Add to your `.env` file:
```bash
CONTENT_PROVIDER=next-mdx-remote
```

### 2. Build Content

First time setup:
```bash
# Build MDX content
pnpm build:content

# The build script will automatically detect the provider from env
```

### 3. Update Development Workflow

The development commands remain the same:
```bash
# Start development with watch mode
pnpm dev

# This runs both Next.js dev server and MDX watch mode in parallel
```

### 4. Production Build

```bash
# Full production build
pnpm build

# This will:
# 1. Build MDX content (using next-mdx-remote)
# 2. Generate sitemap and RSS
# 3. Build Next.js application
```

### 5. Verify Migration

Check that content is loading correctly:
```bash
# Start the production server
pnpm start

# Visit your content pages:
# - http://localhost:3000/blogs
# - http://localhost:3000/products
# - http://localhost:3000/case-studies
```

## Key Differences

### File Generation

| Contentlayer2 | Next.js MDX Remote |
|--------------|-------------------|
| `.contentlayer/` | `.mdx-compiled/` |
| TypeScript types | JSON bundles |
| Runtime parsing | Pre-compiled |

### Build Output

Next.js MDX Remote generates:
- `content-bundle.json` - Full content data
- `content-bundle.min.json` - Minified for production
- `content-manifest.json` - Build metadata

### Development Experience

- **Hot Reload**: Both providers support hot reload in development
- **Type Safety**: Contentlayer2 provides TypeScript types, Next.js MDX Remote uses runtime validation
- **Build Speed**: Next.js MDX Remote is generally faster for large content bases

## Configuration Options

### Build Script Options

```bash
# Watch mode for development
tsx scripts/build-mdx-content.ts --watch

# Verbose output for debugging
tsx scripts/build-mdx-content.ts --verbose
```

### Provider Configuration

In `src/services/content/types/config.ts`:
```typescript
providerConfig: {
  'next-mdx-remote': {
    contentDir: 'content',      // Source directory
    compiledDir: '.mdx-compiled' // Output directory
  }
}
```

## Rollback Instructions

If you need to switch back to Contentlayer2:

1. Update environment variable:
```bash
CONTENT_PROVIDER=contentlayer2
```

2. Rebuild content:
```bash
pnpm contentlayer build
```

3. Restart your development server:
```bash
pnpm dev
```

## Troubleshooting

### Content Not Loading

1. Check that `.mdx-compiled/` directory exists
2. Verify `CONTENT_PROVIDER` is set correctly
3. Run `pnpm build:content` manually
4. Check console for any build errors

### Build Errors

1. Clear compiled content:
```bash
rm -rf .mdx-compiled
```

2. Rebuild:
```bash
pnpm build:content
```

### Type Errors

The Next.js MDX Remote provider uses runtime types instead of generated TypeScript types. Your IDE may show some type warnings that can be safely ignored.

## Performance Comparison

| Metric | Contentlayer2 | Next.js MDX Remote |
|--------|--------------|-------------------|
| Build Time (100 files) | ~15s | ~8s |
| Memory Usage | ~300MB | ~150MB |
| Runtime Lookup | O(n) | O(1) |
| Cloudflare Workers | Limited | Full Support |

## Support

For issues or questions:
- Check the [main documentation](../../README.md)
- Review test files in `__tests__/services/content/providers/next-mdx-remote/`
- Submit issues to the project repository