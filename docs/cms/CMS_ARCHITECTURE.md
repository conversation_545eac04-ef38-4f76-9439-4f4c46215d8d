# Content Service Architecture

## Overview

This document describes the content management architecture of the ShipAny project. We use a unified content service architecture that provides a clean abstraction over different CMS providers while maintaining flexibility and performance.

## Architecture Principles

### 1. Provider Independence
- Abstract layer isolates specific CMS implementations
- Supports multiple CMS backends (Contentlayer2, <PERSON>rapi, <PERSON>ity, Next.js MDX)
- Unified API interface for easy provider switching

### 2. Type Safety
- Comprehensive TypeScript type definitions
- Compile-time type checking
- Automatic type inference and IntelliSense

### 3. Performance Optimization
- Cloudflare Workers compatible (lightweight design)
- Static generation support for Next.js
- Efficient content queries and caching

### 4. SEO Friendly
- Automatic SEO metadata generation
- Multi-language sitemap support
- RSS feed generation
- Open Graph and Twitter Cards support

## Directory Structure

```
src/services/content/
├── core/                    # Core service components
│   ├── content-service.ts   # Main service class
│   ├── provider-factory.ts  # Provider factory
│   ├── config.ts           # Configuration manager
│   └── index.ts            # Core exports
├── providers/              # CMS provider implementations
│   ├── contentlayer2/      # Contentlayer2 provider
│   ├── strapi/             # Strapi provider (placeholder)
│   ├── sanity/             # Sanity provider (placeholder)
│   └── nextjs-mdx/         # Next.js MDX provider (placeholder)
├── modules/                # Business logic modules
│   ├── language-switching.ts # Language switching logic
│   ├── seo-metadata.ts     # SEO metadata generation
│   ├── static-generation.ts # Static generation support
│   └── content-relations.ts # Content relations
├── types/                  # Type definitions
│   ├── content.ts          # Content types
│   ├── provider.ts         # Provider interface
│   ├── config.ts           # Configuration types
│   └── index.ts            # Type exports
├── utils/                  # Utility functions
│   ├── url-generation.ts   # URL utilities
│   ├── content-detection.ts # Content detection
│   └── validation.ts       # Validation functions
└── index.ts                # Main entry point
```

## Core Components

### 1. ContentService (`core/content-service.ts`)

The main service class that provides content operations:

```typescript
export class ContentService {
  async getContent<T>(type: ContentType, slug: string, locale: string): Promise<T | null>
  async getContentList<T>(type: ContentType, locale: string, options?: QueryOptions): Promise<T[]>
  async getAllContentSlugs(type: ContentType): Promise<Array<{slug: string; locale: string}>>
  async contentExists(type: ContentType, slug: string, locale: string): Promise<boolean>
  async getAvailableLanguages(type: ContentType, slug: string): Promise<LanguageVersion[]>
  async getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null>
}
```

### 2. Provider Interface (`types/provider.ts`)

The provider interface defines how different CMS backends integrate with our system:

```typescript
export interface ContentProvider {
  readonly name: string
  readonly version: string
  
  getContent<T extends ContentItem>(type: ContentType, slug: string, locale: string): Promise<T | null>
  getContentList<T extends ContentItem>(type: ContentType, locale: string, options?: QueryOptions): Promise<T[]>
  getAllContentSlugs(type: ContentType): Promise<Array<{slug: string; locale: string}>>
  contentExists(type: ContentType, slug: string, locale: string): Promise<boolean>
  getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null>
  getContentMetadata(type: ContentType, slug: string, locale: string): Promise<ContentMetadata | null>
  getAvailableLanguages(type: ContentType, slug: string): Promise<LanguageVersion[]>
}
```

### 3. ProviderFactory (`core/provider-factory.ts`)

Factory pattern for creating provider instances:

```typescript
export class ProviderFactory {
  static create(providerName: string, config: any): ContentProvider {
    switch (providerName) {
      case 'contentlayer2':
        return new Contentlayer2Provider()
      case 'strapi':
        return new StrapiProvider(config)
      case 'sanity':
        return new SanityProvider(config)
      case 'nextjs-mdx':
        return new NextJSMDXProvider(config)
      default:
        throw new Error(`Unknown provider: ${providerName}`)
    }
  }
}
```

### 4. Current Provider Status

| Provider | Status | Description |
|----------|--------|-------------|
| Contentlayer2 | ✅ Implemented | File-based MDX processing, Next.js 15 support |
| Next.js MDX | 🚧 Placeholder | Native Next.js MDX support |
| Strapi | 🚧 Placeholder | Open-source Headless CMS |
| Sanity | 🚧 Placeholder | Real-time collaborative CMS |

## Business Modules

### 1. Language Switching (`modules/language-switching.ts`)

Handles intelligent language switching with fallback strategies:

```typescript
export async function handleContentLanguageSwitch({
  currentPath,
  currentLocale,
  targetLocale,
  context
}: LanguageSwitchParams): Promise<LanguageSwitchResult> {
  // Check if content exists in target language
  // Fall back to list page if not available
  // Return appropriate URL and strategy
}
```

### 2. SEO Metadata (`modules/seo-metadata.ts`)

Generates comprehensive SEO metadata:

```typescript
export async function generateSEOMetadata(
  content: ContentItem,
  locale: string,
  type: ContentType
): Promise<SEOMetadata> {
  // Generate title, description, keywords
  // Create Open Graph and Twitter Card data
  // Build structured data (JSON-LD)
}
```

### 3. Static Generation (`modules/static-generation.ts`)

Provides utilities for Next.js static generation:

```typescript
export async function generateStaticParams(
  type: ContentType
): Promise<Array<{ locale: string; slug: string }>> {
  // Get all content slugs for static generation
  return contentService.getAllContentSlugs(type)
}

export async function getAllContent<T extends ContentItem>(
  type: ContentType
): Promise<T[]> {
  // Get all content across all locales
}
```



## Usage Examples

### 1. Basic Content Operations

```typescript
import { 
  getContent, 
  getContentList, 
  contentExists,
  getAvailableLanguageVersions 
} from '@/services/content'

// Get single content item
const blog = await getContent<BlogContent>('blog', 'my-post', 'en')

// Get content list with options
const products = await getContentList<ProductContent>('product', 'zh', {
  sortBy: 'publishedAt',
  order: 'desc',
  featured: true,
  limit: 10
})

// Check if content exists
const exists = await contentExists('case-study', 'success-story', 'en')

// Get available language versions
const languages = await getAvailableLanguageVersions('blog', 'my-post')
```

### 2. Language Switching

```typescript
import { handleContentLanguageSwitch } from '@/services/content'

// Handle language switch with intelligent fallback
const result = await handleContentLanguageSwitch({
  currentPath: '/blogs/my-post',
  currentLocale: 'en',
  targetLocale: 'zh',
  context: {
    contentType: 'blog',
    slug: 'my-post'
  }
})

// result.url will be either the translated content URL 
// or the list page URL if content doesn't exist
```

### 3. SEO Metadata Generation

```typescript
import { generateSEOMetadata } from '@/services/content'

// Generate comprehensive SEO metadata
const metadata = await generateSEOMetadata(
  content,
  'en',
  'blog'
)

// Use in Next.js metadata
export async function generateMetadata() {
  return metadata
}
```



## Extension Guide

### Adding a New CMS Provider

1. **Create Provider Class**
   ```typescript
   // src/services/content/providers/new-cms/provider.ts
   export class NewCMSProvider implements ContentProvider {
     readonly name = 'new-cms'
     readonly version = '1.0.0'
     
     // Implement all required methods
     async getContent<T>(...): Promise<T | null> { ... }
     async getContentList<T>(...): Promise<T[]> { ... }
     // etc.
   }
   ```

2. **Register in ProviderFactory**
   ```typescript
   // src/services/content/core/provider-factory.ts
   case 'new-cms':
     return new NewCMSProvider(config)
   ```

3. **Update Type Definitions**
   ```typescript
   // src/services/content/types/config.ts
   export type ProviderType = 'contentlayer2' | 'strapi' | 'sanity' | 'new-cms'
   ```



## Best Practices

### 1. Error Handling
- All async operations should have proper error handling
- Use custom error types for better error messages
- Implement graceful degradation

### 2. Performance Optimization
- Design for Cloudflare Workers (lightweight, no heavy dependencies)
- Implement efficient content queries
- Use static generation where possible

### 3. Type Safety
- Leverage TypeScript's type system fully
- Avoid using `any` type
- Provide complete type definitions for all public APIs

### 4. Testing Strategy
- Unit tests for each provider
- Integration tests for component interactions
- Use mock data for testing

## Migration Guide

### Switching CMS Providers

1. **Update Configuration**
   ```typescript
   // .env
   CONTENT_PROVIDER=strapi
   STRAPI_API_URL=https://your-strapi-instance.com
   STRAPI_API_TOKEN=your-token
   ```

2. **Implement New Provider**
   - Create provider class implementing ContentProvider interface
   - Map API responses to our content types
   - Handle authentication and API calls

3. **Test Content Operations**
   - Verify all content queries work
   - Check language switching functionality
   - Test SEO metadata generation

4. **Deploy and Monitor**
   - Deploy with new configuration
   - Monitor for errors
   - Verify performance metrics

## Summary

The unified content service architecture provides:

- **Flexibility**: Support for multiple CMS backends
- **Extensibility**: Easy to add new providers and features
- **Performance**: Cloudflare Workers compatible, efficient queries
- **SEO Friendly**: Comprehensive SEO support
- **Type Safety**: Full TypeScript support
- **Maintainability**: Clean architecture with clear separation of concerns

This architecture allows us to:
- Switch between different CMS providers without changing application code
- Maintain consistent content operations across the application
- Scale from file-based MDX to full headless CMS solutions
- Deploy to various platforms (Vercel, Cloudflare Workers, Docker)
