# MDX 内容图片管理指南

## 概述

本项目的 MDX 内容系统使用外部图片存储服务，而不是将图片存储在项目目录中。这种方式有以下优势：

- 减少项目体积
- 提高构建速度
- 更好的 CDN 分发
- 灵活的图片管理

## 图片存储策略

### 推荐的图片存储服务

1. **云存储服务**
   - AWS S3
   - 阿里云 OSS
   - 腾讯云 COS
   - 七牛云

2. **图片 CDN 服务**
   - Cloudinary
   - ImageKit
   - Unsplash (用于占位符)

3. **免费图片服务**
   - Unsplash
   - Pexels
   - Pixabay

## MDX 文件中的图片配置

### Frontmatter 图片字段

每个 MDX 文件的 frontmatter 中包含以下图片字段：

```yaml
---
title: "文章标题"
slug: "article-slug"
description: "文章描述"
coverImage: "https://your-cdn.com/images/cover.jpg"
author: "作者名称"
authorImage: "https://your-cdn.com/images/author.jpg"
publishedAt: "2025-01-17"
featured: true
tags: ["标签1", "标签2"]
---
```

### 图片字段说明

- **coverImage**: 文章封面图片，建议尺寸 800x400px
- **authorImage**: 作者头像，建议尺寸 100x100px，圆形裁剪

### 当前使用的占位符图片

项目中目前使用 Unsplash 作为占位符图片服务：

```yaml
# 产品相关
coverImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop"

# 博客相关  
coverImage: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop"

# 案例研究相关
coverImage: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop"

# 作者头像
authorImage: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
```

## 图片管理工作流程

### 1. 准备图片

- **封面图片**: 800x400px，JPG/PNG 格式
- **作者头像**: 100x100px，JPG/PNG 格式
- **内容图片**: 根据需要调整尺寸

### 2. 上传到存储服务

1. 选择合适的存储服务
2. 创建存储桶/空间
3. 配置 CDN 加速
4. 上传图片文件

### 3. 获取图片 URL

上传完成后获取图片的公开访问 URL，格式如：

```text
https://your-cdn.com/images/filename.jpg
```

### 4. 更新 MDX 文件

将获取的 URL 更新到对应的 MDX 文件中：

```yaml
---
coverImage: "https://your-cdn.com/images/your-cover.jpg"
authorImage: "https://your-cdn.com/images/your-author.jpg"
---
```

### 5. 重新构建内容

```bash
# 重新构建 Contentlayer
npx contentlayer build

# 或者重启开发服务器
pnpm dev
```

## 图片优化建议

### 1. 尺寸优化

- 封面图片：800x400px
- 作者头像：100x100px
- 内容图片：根据实际需要

### 2. 格式选择

- **JPG**: 适合照片和复杂图像
- **PNG**: 适合图标和简单图形
- **WebP**: 现代浏览器的最佳选择

### 3. 压缩优化

- 使用适当的压缩比例
- 考虑使用 WebP 格式
- 启用 CDN 的自动优化功能

## 故障排除

### 图片不显示

1. **检查 URL 是否正确**
   - 确保 URL 可以在浏览器中直接访问
   - 检查是否有拼写错误

2. **检查权限设置**
   - 确保图片文件是公开可访问的
   - 检查存储服务的权限配置

3. **检查 CORS 设置**
   - 如果使用自定义域名，确保 CORS 配置正确

4. **重新构建内容**

   ```bash
   rm -rf .contentlayer
   npx contentlayer build
   ```

### 图片加载缓慢

1. **使用 CDN 加速**
2. **优化图片大小**
3. **考虑使用 WebP 格式**
4. **启用浏览器缓存**

## 最佳实践

1. **统一命名规范**

   ```text
   covers/blog-getting-started.jpg
   covers/product-ai-generator.jpg
   authors/team-member-1.jpg
   ```

2. **版本管理**
   - 为图片文件添加版本号或时间戳
   - 避免直接覆盖现有图片

3. **备份策略**
   - 定期备份图片文件
   - 使用多个存储服务作为备份

4. **性能监控**
   - 监控图片加载速度
   - 定期检查图片可访问性

## 示例配置

### 完整的 MDX 文件示例

```mdx
---
title: "AI 内容生成器使用指南"
slug: "ai-content-generator-guide"
description: "详细介绍如何使用 AI 内容生成器创建高质量内容"
coverImage: "https://your-cdn.com/covers/ai-generator-guide.jpg"
author: "产品团队"
authorImage: "https://your-cdn.com/authors/product-team.jpg"
publishedAt: "2025-01-17"
featured: true
tags: ["ai", "教程", "内容生成"]
---

# AI 内容生成器使用指南

这里是文章内容...

![示例图片](https://your-cdn.com/content/example-image.jpg)
```

通过遵循这个指南，你可以有效地管理 MDX 内容中的图片，确保网站的性能和用户体验。
