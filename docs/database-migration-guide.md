# 数据库迁移指南

> 从 Supabase 到 Drizzle ORM 的完整迁移记录

## 项目概述

本项目基于脚手架模板开发，将数据库层从 Supabase-js 迁移到 Drizzle ORM，实现更好的类型安全和性能。

## 迁移内容

### 1. 数据库 Schema 迁移

#### 1.1 新增表结构

在 `src/db/schema.ts` 中定义两个核心表：

```typescript
// AI Tasks 表 - AI 任务管理
export const aiTasks = pgTable("ai_tasks", {
  id: serial().primaryKey(),
  task_id: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  task_type: varchar({ length: 50 }).notNull(),
  status: varchar({ length: 50 }).notNull(),
  created_at: timestamp({ withTimezone: true }).notNull().defaultNow(),
  updated_at: timestamp({ withTimezone: true }).notNull().defaultNow(),
  completed_at: timestamp({ withTimezone: true }),
  params: jsonb().notNull(),
  results: jsonb(),
  error: text(),
  model_id: varchar({ length: 100 }).notNull(),
  provider: varchar({ length: 50 }).notNull(),
  provider_task_id: varchar({ length: 255 }),
  progress: integer().notNull().default(0),
}, (table) => [
  index("idx_ai_tasks_user_uuid").on(table.user_uuid),
  index("idx_ai_tasks_status").on(table.status),
  index("idx_ai_tasks_created_at").on(table.created_at),
  index("idx_ai_tasks_model_id").on(table.model_id),
]);

// Files 表 - 文件管理
export const files = pgTable("files", {
  id: serial().primaryKey(),
  file_id: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  task_id: varchar({ length: 255 }),
  storage_key: varchar({ length: 500 }).notNull(),
  file_name: varchar({ length: 255 }).notNull(),
  mime_type: varchar({ length: 100 }).notNull(),
  file_size: bigint({ mode: "number" }).notNull(),
  file_category: varchar({ length: 50 }).notNull(),
  access_level: varchar({ length: 50 }).notNull(),
  status: varchar({ length: 50 }).notNull().default("active"),
  created_at: timestamp({ withTimezone: true }).notNull().defaultNow(),
  expires_at: timestamp({ withTimezone: true }),
  metadata: jsonb(),
}, (table) => [
  index("idx_files_user_uuid").on(table.user_uuid),
  index("idx_files_task_id").on(table.task_id),
  index("idx_files_status").on(table.status),
  index("idx_files_file_category").on(table.file_category),
  index("idx_files_access_level").on(table.access_level),
  index("idx_files_created_at").on(table.created_at),
  index("idx_files_expires_at").on(table.expires_at),
  index("idx_files_storage_key").on(table.storage_key),
  index("idx_files_user_category_status").on(table.user_uuid, table.file_category, table.status),
]);
```

#### 1.2 设计要点

- **时间戳**: 使用 `timestamp({ withTimezone: true })` 确保时区一致性
- **索引优化**: 基于查询模式设计的 13 个索引
- **类型安全**: 利用 Drizzle 的类型推断系统
- **JSON 存储**: 使用 `jsonb()` 存储复杂数据结构

### 2. Model 层重构

#### 2.1 AI Tasks Model (`src/models/ai-tasks.ts`)

重构所有数据库操作函数：

```typescript
// 核心 CRUD 操作
export async function createTask(taskData: TaskCreationData): Promise<string>
export async function getTask(taskId: string): Promise<AiTask | null>
export async function getUserTasks(userUuid: string, limit: number = 50): Promise<AiTask[]>

// 状态管理
export async function updateTaskStatus(taskId: string, status: TaskStatusType, progress: number = 0): Promise<void>
export async function completeTaskWithResults(taskId: string, results: any): Promise<void>
export async function failTaskWithError(taskId: string, error: string): Promise<void>
```

#### 2.2 File Model (`src/models/file.ts`)

重构文件管理操作：

```typescript
// 文件生命周期
export async function createFile(fileData: FileCreationData): Promise<string>
export async function getFileById(fileId: string): Promise<FileRecord | null>
export async function getActiveUserFilesPaginated(userUuid: string, page: number, limit: number): Promise<{ files: FileRecord[]; totalCount: number }>

// 清理操作
export async function getExpiredFiles(): Promise<FileRecord[]>
export async function hardDeleteFileById(fileId: string): Promise<void>
export async function getUserStorageStats(userUuid: string): Promise<{ totalFiles: number; totalSize: number }>
```

### 3. 数据库迁移

#### 3.1 生成迁移

```bash
npm run db:generate
```

生成 `src/db/migrations/0001_*.sql` 包含：

- 两个新表的完整定义
- 所有索引创建
- 约束和默认值设置

#### 3.2 应用迁移

```bash
npm run db:migrate  # 生产环境
npm run db:push     # 开发环境
```

## 技术特点

### 1. 性能优化

- **复合索引**: 支持常用查询组合
- **选择性索引**: 优化查询性能
- **高效分页**: 使用 `limit()` 和 `offset()`

### 2. 类型安全

```typescript
// 自动类型推断
type Task = typeof aiTasks.$inferSelect;
type NewTask = typeof aiTasks.$inferInsert;
```

### 3. 向后兼容

- 保持所有现有函数签名
- 返回值格式一致
- BigInt 自动转换为 Number

## 部署环境适配

### Cloudflare Workers

```toml
# wrangler.toml
[vars]
DATABASE_URL = "postgresql://[connection]"
```

### Vercel

```bash
# 环境变量
DATABASE_URL = "postgresql://[connection]"
```

### 本地开发

标准数据库连接配置，开发环境测试通过。

## 成功指标

- ✅ 功能完整性：所有原有功能正常运行
- ✅ 性能基准：查询性能不低于原有系统
- ✅ 类型安全：编译时零类型错误
- 🔄 部署成功：多环境部署验证

## 总结

本次迁移成功实现了：

1. **最小入侵**: 保持模板完整性
2. **类型安全**: 利用 Drizzle 编译时检查
3. **性能优化**: 合理的索引设计
4. **环境兼容**: 支持多种部署环境

迁移为项目带来了更好的类型安全、开发体验和长期可维护性。
