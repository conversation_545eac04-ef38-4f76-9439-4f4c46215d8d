# Docker开发环境快速参考

## 🚀 快速启动

```bash
# 1. 配置认证（一次性设置）
# 编辑 .env.docker，填入 CLOUDFLARE_API_TOKEN 和 CLOUDFLARE_ACCOUNT_ID

# 2. 启动环境
docker-compose up -d

# 3. 进入容器
docker-compose exec dev bash

# 4. 验证环境
wrangler whoami
node --version  # 应显示 v20.x.x
```

## 📋 常用命令

### Docker操作

```bash
docker-compose up -d              # 启动环境
docker-compose down               # 停止环境
docker-compose ps                # 查看状态
docker-compose logs -f dev       # 查看日志
docker-compose exec dev bash     # 进入容器
```

### 开发命令（容器内）

```bash
pnpm dev                         # 启动开发服务器
pnpm build                       # 构建项目
pnpm cf:preview                  # Cloudflare预览部署
pnpm cf:deploy                   # Cloudflare生产部署
wrangler tail                    # 查看Cloudflare日志
```

## 🎯 CMS重构工作流

```bash
# 1. 启动环境
docker-compose up -d
docker-compose exec dev bash

# 2. 开发
pnpm dev  # 访问 http://localhost:3000

# 3. 测试部署
pnpm cf:preview

# 4. 停止
exit
docker-compose down
```

## 🔧 故障排查

| 问题 | 解决方案 |
|------|----------|
| 容器启动失败 | `docker-compose build --no-cache` |
| wrangler认证失败 | 检查 `.env.docker` 中的API Token |
| 端口冲突 | 修改 `docker-compose.yml` 端口映射 |
| Node.js版本错误 | 确保使用 `node:20-alpine` |

## ✅ 成功验证

- ✅ `docker-compose ps` 显示容器运行
- ✅ `wrangler whoami` 显示账户信息
- ✅ `node --version` 显示 v20.x.x
- ✅ 开发服务器可访问：http://localhost:3000

## 📁 关键文件

- `docker-compose.yml` - Docker编排配置
- `Dockerfile.dev` - 开发环境容器（Node.js 20）
- `.env.docker` - Docker环境变量
- `docs/docker/DOCKER_DEVELOPMENT_GUIDE.md` - 完整开发指南
