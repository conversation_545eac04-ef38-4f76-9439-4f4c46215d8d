# 代码注释指南

## 概述

本文档概述了 MDX 多语言内容管理系统中使用的注释标准。所有代码注释和日志使用英文，而面向用户的内容和文档可以使用中文。

## 注释类型和标准

### 1. 文件级注释

每个主要文件都应该以全面的 header 注释开始：

```typescript
/**
 * File Purpose and Description
 *
 * Detailed explanation of what this file does, its role in the system,
 * and any important architectural decisions or patterns used.
 */
```

### 2. Function/Component 注释

所有导出的 function 和 component 都应该有 JSDoc 风格的注释：

```typescript
/**
 * Brief description of what the function does
 *
 * More detailed explanation if needed, including any important
 * implementation details or usage patterns.
 *
 * @param paramName - Description of the parameter
 * @param optionalParam - Description of optional parameter
 * @returns Description of what the function returns
 */
export function myFunction(paramName: string, optionalParam?: boolean): ReturnType {
  // Implementation
}
```

### 3. Interface/Type 注释

复杂的 type 和 interface 应该被文档化：

```typescript
interface MyInterface {
  // Brief description of what this property represents
  propertyName: string

  // Optional property with explanation of when it's used
  optionalProperty?: number
}
```

### 4. 内联注释

使用内联注释来解释复杂逻辑或不明显的代码：

```typescript
// Extract language from file path (e.g., 'blogs/en/post.mdx' -> 'en')
const language = doc._raw.flattenedPath.split('/')[1]

// Generate URL with proper locale handling (English has no prefix)
const url = locale === 'en' ? `${baseUrl}${page}` : `${baseUrl}/${locale}${page}`
```

### 5. 区域注释

使用区域注释来组织文件内的代码：

```typescript
// ============================================================================
// Helper Functions
// ============================================================================

// ============================================================================
// Main Component
// ============================================================================

// ============================================================================
// Export
// ============================================================================
```

## 使用的特定模式

### 1. Contentlayer 配置

```typescript
/**
 * Factory function to create common document type configuration
 *
 * @param name - The document type name (e.g., 'Blog', 'Product')
 * @param pattern - The directory pattern to match files (e.g., 'blogs', 'products')
 * @returns Document type configuration object
 */
```

### 2. React Component

```typescript
/**
 * Component Name and Purpose
 *
 * Detailed description of what the component renders and its
 * role in the application. Include any important props or
 * usage patterns.
 *
 * @param propName - Description of the prop
 * @returns JSX element
 */
```

### 3. 工具函数

```typescript
/**
 * Generate canonical URL for SEO purposes
 *
 * Creates a full URL with the correct locale prefix for SEO meta tags.
 * English content doesn't include a locale prefix.
 *
 * @param path - The relative path
 * @param locale - Optional locale override
 * @returns Full canonical URL
 */
```

### 4. API Route 和 Server Function

```typescript
/**
 * API Route Handler
 *
 * Description of what this API endpoint does, what it accepts,
 * and what it returns. Include any authentication or validation
 * requirements.
 *
 * @param request - The incoming request object
 * @returns Response with appropriate status and data
 */
```

## 注释质量指导原则

### 应该做的

- **描述性强**：解释"为什么"而不仅仅是"什么"
- **使用正确的语法**：注释应该写得好且专业
- **保持时效性**：代码更改时更新注释
- **解释复杂逻辑**：如果你花时间理解，就注释它
- **文档化假设**：解释代码做出的任何假设

### 不应该做的

- **不要陈述显而易见的**：对 `counter++` 写 `// Increment counter`
- **不要使用过时的注释**：删除或更新陈旧的注释
- **不要过度注释**：不是每一行都需要注释
- **不要用注释来禁用代码**：使用版本控制代替

## 语言标准

### 代码注释（英文）

```typescript
// Generate URL with proper locale handling
// Filter blogs by current locale and sort by publication date
// Add hreflang attributes for all language versions
```

### Console 日志（英文）

```typescript
console.log('✅ Sitemap generated successfully!')
console.log(`✅ RSS feed for ${locale} generated successfully!`)
console.error('Failed to generate content:', error)
```

### 错误消息（英文）

```typescript
throw new Error(`Slug is required for ${section}.${action}`)
throw new Error('Invalid locale provided')
```

## 特定文件的注释示例

### 配置文件

```typescript
/**
 * Contentlayer Configuration
 *
 * This file configures Contentlayer to process MDX files for our multi-language
 * content management system. It defines document types for blogs, products, and
 * case studies with consistent field structures and computed properties.
 */
```

### Component 文件

```typescript
/**
 * MDX Rendering Component
 *
 * This component provides a consistent way to render MDX content with custom
 * styling and component mappings. It uses Contentlayer's useMDXComponent hook
 * to render the compiled MDX code with custom React components.
 */
```

### 语言切换组件示例

```typescript
/**
 * ContentLanguageIndicator Component
 *
 * PURPOSE:
 * This component is specifically designed for CONTENT DETAIL PAGE HEADERS.
 * It shows language availability with clear visual indicators and provides
 * compact language switching.
 *
 * USAGE LOCATIONS:
 * - Blog detail pages: /blogs/[slug] (top-right corner)
 * - Product detail pages: /products/[slug] (top-right corner)
 * - Case study detail pages: /case-studies/[slug] (top-right corner)
 *
 * KEY DIFFERENCES FROM LanguageVersions:
 * - Shows availability status with icons (✓/✗)
 * - More compact design for header placement
 * - Uses Material Design icons (MdLanguage, MdCheck, MdClose)
 *
 * @param className - Additional CSS classes for styling
 * @param variant - Display mode ('compact' for headers, 'full' for detailed view)
 */
```

### 工具文件

```typescript
/**
 * Route Utilities
 *
 * This module provides utilities for generating type-safe routes and URLs
 * for our multi-language application. It includes path generation functions,
 * canonical URL builders, and internationalization helpers that ensure
 * consistent routing across different locales and content types.
 */
```

### Script 文件

```typescript
/**
 * Sitemap Generation Script
 *
 * This script generates a comprehensive sitemap.xml file that includes:
 * - Static pages (home, pricing, showcase)
 * - MDX content pages (blogs, products, case studies)
 * - Multi-language support with hreflang attributes
 * - Proper priority and change frequency settings
 */
```

## 维护

- 在代码审查期间检查注释
- 重构代码时更新注释
- 删除过时或冗余的注释
- 确保整个代码库的一致性

这个注释标准确保我们的代码库对所有团队成员保持可维护性和可理解性，无论他们对具体实现细节的熟悉程度如何。
